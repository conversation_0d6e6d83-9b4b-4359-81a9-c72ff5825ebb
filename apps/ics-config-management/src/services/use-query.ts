import {
  useQuery,
  useMutation,
  useQueryClient,
  UseQueryOptions,
  UseQueryResult,
  UseMutationResult,
  UseMutationOptions,
} from '@tanstack/react-query';
import {
  AssignmentItem,
  Assignments,
  ConfigAssignmentStats,
  ConfigInstanceStats,
  ConfigInstanceItemsStats,
  HierarchyLevelEnum,
  RevisionsResponse,
  RevisionResultItem,
  InstancesResponse,
  DeviceConfig,
  InstancesRequest,
  InstanceItemStats,
  GetUsersResponse,
  GetUsersRequest,
  RevisionPublishDeploymentType,
  DeviceAssetResponse,
  SiteAssetResponse,
  SiteTagAssetResponse,
  UpdateSchedulePayload,
  GetMaintenanceWindow,
  ApprovalRejectPayload,
  EntitySettingsPayload,
  EntityPendingResponse,
} from '../constants/types';
import {
  ScheduleAlertRequest,
  ScheduleAlertResponse,
} from '../components/ScheduleDeploymentAlert/types';
import {
  SchedulesRequest,
  ScheduleResponse,
} from '../components/ScheduleList/types';
import {
  getAssignments,
  getDeploymentDetails,
  getDeployments,
  getDeploymentStats,
  getDeviceList,
  getInstanceDetails,
  getInstanceSchema,
  getSiteList,
  getSiteTagList,
  getConfigInstanceStat,
  getConfigInstanceAssignmentStats,
  getRevisions,
  getOneRevision,
  putUpdateInstance,
  postPublishRevisions,
  getEntityHierarchyStats,
  putOneRevision,
  getInstances,
  deleteConfigInstance,
  getUserConfig,
  getInstanceItemStats,
  getConfigInstanceItemStats,
  getUsers,
  postAssignments,
  deleteAssignments,
  getSchedules,
  putUpdateSchedule,
  getConfigScheduled,
  postScheduledRevisions,
  getMaintenanceWindow,
  getDeviceCountByTenant,
  getEntityPendingValues,
  getEntitySettingsStatus,
  putApprovalReject,
  putEntittySettings,
  getEntitySettings,
  getEntityPendingSettings,
  postEntityPending,
  getUserDetails,
} from './api-request';

export const useGetDevices = (
  {
    pageIndex,
    pageSize,
    fields,
    filters,
  }: {
    pageIndex?: number;
    pageSize?: number;
    fields?: string[];
    filters?: {
      name: {
        $containsi: string;
      };
    };
  },
  queryOpts?: UseQueryOptions<DeviceAssetResponse>
): UseQueryResult<DeviceAssetResponse> =>
  useQuery<DeviceAssetResponse>(
    ['getDevicesList', pageIndex, pageSize, fields, filters],
    ({ signal }) =>
      getDeviceList({ signal, pageIndex, pageSize, fields, filters }),
    queryOpts
  );

export const useGetDeviceCountByTenant = (
  {
    pageIndex,
    pageSize,
    fields,
  }: {
    pageIndex?: number;
    pageSize?: number;
    fields?: string[];
  },
  queryOpts?: UseQueryOptions<DeviceAssetResponse>
): UseQueryResult<DeviceAssetResponse> =>
  useQuery<DeviceAssetResponse>(
    ['getDeviceCountByTenant', pageIndex, pageSize, fields],
    () => getDeviceCountByTenant({ pageIndex, pageSize, fields }),
    queryOpts
  );

export const useGetSites = (
  {
    pageIndex,
    pageSize,
    fields,
    filters,
  }: {
    pageIndex?: number;
    pageSize?: number;
    fields?: string[];
    filters?: {
      siteName: {
        $containsi: string;
      };
    };
  },
  queryOpts?: UseQueryOptions<SiteAssetResponse>
): UseQueryResult<SiteAssetResponse> =>
  useQuery<SiteAssetResponse>(
    ['getSitesList', pageIndex, pageSize, fields, filters],
    ({ signal }) =>
      getSiteList({ signal, pageIndex, pageSize, fields, filters }),
    queryOpts
  );

export const useGetSiteTags = (
  {
    pageIndex,
    pageSize,
    fields,
    filters,
  }: {
    pageIndex?: number;
    pageSize?: number;
    fields?: string[];
    filters?: {
      tagName: {
        $containsi: string;
      };
    };
  },
  queryOpts?: UseQueryOptions<SiteTagAssetResponse>
): UseQueryResult<SiteTagAssetResponse> =>
  useQuery<SiteTagAssetResponse>(
    ['getSiteTagsList', pageIndex, pageSize, fields, filters],
    ({ signal }) =>
      getSiteTagList({ signal, pageIndex, pageSize, fields, filters }),
    queryOpts
  );

export const useGetAssignments = (id: string) =>
  useQuery(['getAssignments', id], () => getAssignments(id), {
    select(data) {
      const LEVELS = HierarchyLevelEnum;
      type AssignmentInLevel = {
        [key in HierarchyLevelEnum]: AssignmentItem[];
      };

      const assignmentsTemplate = [
        {
          hierarchyLevel: 'Site',
          assignments: [],
        },
        {
          hierarchyLevel: 'SiteTag',
          assignments: [],
        },
        {
          hierarchyLevel: 'Tenant',
          assignments: [],
        },
        {
          hierarchyLevel: 'Device',
          assignments: [],
        },
      ] as Assignments[];

      data?.forEach(res => {
        assignmentsTemplate.forEach((assignment, index) => {
          if (assignment.hierarchyLevel === res.hierarchyLevel) {
            assignmentsTemplate[index].assignments = res.assignments;
          }
        });
      });

      const newAssignmentsInLevel = assignmentsTemplate.reduce(
        (newAssignment, { hierarchyLevel, assignments: newAssignments }) => {
          if (Object.values(LEVELS).includes(hierarchyLevel)) {
            // eslint-disable-next-line
            newAssignment[hierarchyLevel] = newAssignments;
          }
          return newAssignment;
        },
        {} as AssignmentInLevel
      );

      return newAssignmentsInLevel;
    },
  });

export const useGetInstanceDetails = (instanceId: string) =>
  useQuery(
    ['getInstanceDetails', instanceId],
    () => getInstanceDetails(instanceId),
    {
      refetchOnWindowFocus: true,
    }
  );

export const useGetInstanceSchema = (instanceId: string) =>
  useQuery(
    ['getInstanceSchema', instanceId],
    () => getInstanceSchema(instanceId),
    {
      refetchOnWindowFocus: true,
    }
  );

export const useGetTopLevelAssignmentStats = (instanceId: string) =>
  useQuery<ConfigInstanceStats>(['getConfigInstanceStat', instanceId], () =>
    getConfigInstanceStat(instanceId)
  );

export const useGetDeployments = (instanceId: string) =>
  useQuery(['getDeployments', instanceId], () => getDeployments(instanceId));

export const useGetDeploymentStats = (
  instanceId: string,
  deploymentId: string
) =>
  useQuery(['getDeploymentStats', deploymentId], () =>
    getDeploymentStats(instanceId, deploymentId)
  );

export const useGetConfigInstanceAssignmentStats = (
  instanceId: string,
  assignmentId: string
) =>
  useQuery<ConfigAssignmentStats>(
    ['getConfigInstanceAssignmentStats', instanceId, assignmentId],
    () => getConfigInstanceAssignmentStats(instanceId, assignmentId)
  );

export const useGetDeploymentDetails = (
  instanceId: string,
  deploymentId: string,
  isOpen: boolean,
  onError: () => void
) =>
  useQuery(
    ['getDeploymentDetails', deploymentId],
    () => getDeploymentDetails(instanceId, deploymentId),
    {
      enabled: isOpen,
      onError: () => onError(),
    }
  );

export const useGetRevisions = (
  instanceId: string
): UseQueryResult<RevisionsResponse> =>
  useQuery<RevisionsResponse>(
    ['getRevisions', instanceId],
    () => getRevisions(instanceId),
    {
      refetchOnWindowFocus: true,
    }
  );

export const useGetEntitySettings = (
  id: string
): UseQueryResult<RevisionsResponse> =>
  useQuery<RevisionsResponse>(
    ['getEntitySettings', id],
    () => getEntitySettings(id),
    {
      refetchOnWindowFocus: true,
    }
  );

export const useGetEntityPendingSettings =
  (): UseQueryResult<EntityPendingResponse> =>
    useQuery<EntityPendingResponse>(
      ['getEntityPendingSettings'],
      () => getEntityPendingSettings(),
      {
        refetchOnWindowFocus: true,
      }
    );

export const useGetRevision = (
  {
    instanceId,
    revisionId,
  }: {
    instanceId: string;
    revisionId: string;
  },
  queryOpts?: UseQueryOptions<RevisionResultItem>
): UseQueryResult<RevisionResultItem> =>
  useQuery(
    ['getRevision', instanceId, revisionId],
    ({ signal }) => getOneRevision({ instanceId, revisionId, signal }),
    queryOpts as unknown
  );

export const usePutRevision = (
  {
    instanceId,
    revisionId,
    displayName,
  }: {
    instanceId: string;
    revisionId: string;
    displayName: string;
  },
  mutationOpts?: UseMutationOptions<RevisionResultItem>,
  signal?: AbortSignal
): UseMutationResult<RevisionResultItem> =>
  useMutation({
    mutationFn: () =>
      putOneRevision({ instanceId, revisionId, displayName }, signal),
    ...mutationOpts,
  });

export const usePutUpdateInstance = (instanceId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: putUpdateInstance,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['getInstanceDetails', instanceId],
      });
    },
  });
};

export const usePostPublishRevisions = () =>
  useMutation({
    mutationFn: postPublishRevisions,
  });

export const usePostEntityPending = () =>
  useMutation({
    mutationFn: postEntityPending,
  });

export const usePostScheduledRevisions = () =>
  useMutation({
    mutationFn: postScheduledRevisions,
  });

export const useGetEntityHierarchyStats = (
  instanceId: string,
  hierarchyType: string,
  hierarchyValue: string
) =>
  useQuery<ConfigAssignmentStats>(
    ['getEntityHierarchyStats', instanceId, hierarchyType, hierarchyValue],
    ({ signal }) =>
      getEntityHierarchyStats(instanceId, hierarchyType, hierarchyValue, signal)
  );

export const useGetInstances = (
  instancesRequest: InstancesRequest,
  queryOpts?: UseQueryOptions<InstancesResponse>
): UseQueryResult<InstancesResponse> =>
  useQuery<InstancesResponse>(
    ['getInstances', instancesRequest],
    ({ signal }) => getInstances(instancesRequest, signal),
    queryOpts
  );

export const useGetEntitySettingsStatus = (
  entityType?: string,
  queryOpts?: UseQueryOptions<any>
): UseQueryResult<any> =>
  useQuery(
    ['getEntitySettingsStatus'],
    ({ signal }) => getEntitySettingsStatus(signal, entityType),
    queryOpts
  );

export const useGetEntityPendingValues = (
  entityType?: string,
  queryOpts?: UseQueryOptions<any>
): UseQueryResult<any> =>
  useQuery(
    ['getEntityPendingValues'],
    ({ signal }) => getEntityPendingValues(signal, entityType),
    queryOpts
  );

export const usePutApprovalReject = (
  mutationOpts?: UseMutationOptions<
    any,
    unknown,
    ApprovalRejectPayload[],
    unknown
  >
): UseMutationResult<any, unknown, ApprovalRejectPayload[], unknown> =>
  useMutation({
    mutationFn: (payload: ApprovalRejectPayload[]) =>
      putApprovalReject(payload),
    ...mutationOpts,
  });

export const usePutEntittySettings = (
  mutationOpts?: UseMutationOptions<
    any,
    unknown,
    EntitySettingsPayload[],
    unknown
  >
): UseMutationResult<any, unknown, EntitySettingsPayload[], unknown> =>
  useMutation({
    mutationFn: (payload: EntitySettingsPayload[]) =>
      putEntittySettings(payload),
    ...mutationOpts,
  });
/**
 * Get assignments from instance (no side-effects)
 *
 * @param {{ instanceId: string }} { instanceId }
 * @param {?UseQueryOptions<Assignments[]>} [queryOpts]
 * @returns {UseQueryResult<Assignments[]>}
 */
export const useGetInstanceAssignments = (
  { instanceId }: { instanceId: string },
  queryOpts?: UseQueryOptions<Assignments[]>
): UseQueryResult<Assignments[]> =>
  useQuery<Assignments[]>(
    ['getAssignments', instanceId],
    () => getAssignments(instanceId),
    queryOpts
  );

export const useGetSchedule = (
  schedulesRequest: SchedulesRequest,
  queryOpts?: UseQueryOptions<ScheduleResponse>
): UseQueryResult<ScheduleResponse> =>
  useQuery<ScheduleResponse>(
    ['getSchedules', schedulesRequest],
    ({ signal }) => getSchedules(schedulesRequest, signal),
    queryOpts
  );
export const useUpdateSchedule = (
  payload: UpdateSchedulePayload,
  mutationOpts?: UseMutationOptions<RevisionResultItem>
): UseMutationResult<UpdateSchedulePayload> =>
  useMutation({
    mutationFn: () => putUpdateSchedule(payload),
    ...mutationOpts,
  });
/**
 * Delete config instance
 *
 * @param {{ instanceId: string }} { instanceId }
 * @param {?UseMutationOptions<number>} [mutationOpts]
 * @returns {UseMutationResult<number>}
 */
export const useDeleteConfigInstance = (
  { instanceId }: { instanceId: string },
  mutationOpts?: UseMutationOptions<number>
): UseMutationResult<number> =>
  useMutation<number>({
    mutationFn: () => deleteConfigInstance({ instanceId }),
    ...mutationOpts,
  });

/**
 * Get available applicationIds,
 * and their config files
 *
 * @param {?UseQueryOptions<DeviceConfig[]>} [queryOpts]
 * @returns {UseQueryResult<DeviceConfig[]>}
 */
export const useGetUserConfig = (
  queryOpts?: UseQueryOptions<DeviceConfig[]>
): UseQueryResult<DeviceConfig[]> =>
  useQuery(['getUserConfig'], ({ signal }) => getUserConfig(signal), queryOpts);

/**
 * Get instance item stats
 *
 * @param {string} instanceId
 * @param {?UseQueryOptions<InstanceItemStats>} [queryOpts]
 * @returns {UseQueryResult<InstanceItemStats>}
 */
export const useGetInstanceItemStats = (
  instanceId: string,
  queryOpts?: UseQueryOptions<InstanceItemStats>
): UseQueryResult<InstanceItemStats> =>
  useQuery(
    ['getInstanceItemStats', instanceId],
    ({ signal }) => getInstanceItemStats(instanceId, signal),
    queryOpts
  );

export const useGetConfigInstanceItemStats = (
  { instanceIds, tenantId }: { instanceIds: string[]; tenantId: string },
  queryOpts?: UseQueryOptions<ConfigInstanceItemsStats>
) =>
  useQuery(
    ['getConfigInstanceItemStats', instanceIds, tenantId],
    ({ signal }) =>
      getConfigInstanceItemStats({ instanceIds, tenantId }, signal),
    queryOpts
  );

/**
 * Get users in company
 *
 * @param {GetUsersRequest} { fullName, pageIndex, pageSize, pending, roles }
 * @param {?UseQueryOptions<GetUsersResponse>} [queryOpts]
 * @returns {UseQueryResult<GetUsersResponse>}
 */
export const useGetUsers = (
  { fullName, pageIndex, pageSize, pending, roles }: GetUsersRequest,
  queryOpts?: UseQueryOptions<GetUsersResponse>
): UseQueryResult<GetUsersResponse> =>
  useQuery(
    ['getUsers', fullName, pageIndex, pageSize, pending, roles],
    ({ signal }) =>
      getUsers({ fullName, pageIndex, pageSize, pending, roles }, signal),
    queryOpts
  );

export const usePostAssignments = (
  {
    instanceId,
    configFileId,
    appDescriptorId,
    appName,
  }: {
    instanceId: string;
    configFileId: number;
    appDescriptorId: number;
    appName: string;
  },
  mutationOpts
): UseMutationResult<Assignments[]> =>
  useMutation<
    Assignments[],
    unknown,
    {
      assignments: Assignments[];
      deploymentType: RevisionPublishDeploymentType;
      mfaCode: string;
      configFileId: number;
      appDescriptorId: number;
      appName: string;
    }
  >(
    ['postAssignments', instanceId],
    ({ assignments, deploymentType, mfaCode }) =>
      postAssignments(instanceId, {
        configAssignments: assignments,
        deploymentType,
        mfaCode,
        configFileId,
        appDescriptorId,
        appName,
      }),
    mutationOpts
  );

export const useDeleteAssignments = (
  {
    instanceId,
  }: {
    instanceId: string;
  },
  mutationOpts: Omit<
    UseMutationOptions<
      Assignments[],
      unknown,
      { deploymentType: RevisionPublishDeploymentType },
      unknown
    >,
    'mutationFn' | 'mutationKey'
  >
): UseMutationResult<Assignments[]> =>
  useMutation<
    Assignments[],
    unknown,
    { deploymentType: RevisionPublishDeploymentType }
  >(
    ['deleteAssignments', instanceId],
    ({ deploymentType }) => deleteAssignments(instanceId, deploymentType),
    mutationOpts
  );

export const useGetConfigScheduled = (
  data: ScheduleAlertRequest,
  queryOpts?: UseQueryOptions<ScheduleAlertResponse>
): UseQueryResult<ScheduleAlertResponse> =>
  useQuery(['getConfigScheduled', data], () => getConfigScheduled(data), {
    ...queryOpts,
    retry: 0,
  });

export const useGetMaintenanceWindow = (
  enabledCondition: boolean
): UseQueryResult<GetMaintenanceWindow> =>
  useQuery(['getMaintenanceWindow'], () => getMaintenanceWindow(), {
    enabled: enabledCondition,
    staleTime: 15 * 1000,
    cacheTime: 15 * 1000,
  });

  export const useGetUserDetails = (userId: string) =>
  useQuery(
    ['getUserDetails', userId],
    () => getUserDetails(userId),
    {
      refetchOnWindowFocus: true,
    }
  );

