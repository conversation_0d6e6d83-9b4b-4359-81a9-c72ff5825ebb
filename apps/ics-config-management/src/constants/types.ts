import {
  InstanceSortOrder,
  InstanceSortOrderBy,
} from '../components/ConfigListSearch/types';
import Tabs from './entities';
import UserRoles from './userRoles';
import FeatureFlags from './featureFlags';

export interface TabProps {
  id: string;
}
export interface LinkTabProps {
  label: string;
  to: string;
  key: Lowercase<TabType | 'instances' | 'schedules' | 'deployments'>;
}

export interface RevisionItem {
  id: string;
  title?: string;
  hash?: string;
  content: string;
  createdDate: string;
  isDraft?: boolean;
  isSchedule?: boolean;
}

export enum RevisionPublishDeploymentType {
  immediate = 'immediate',
  maintenanceWindow = 'maintenance-window',
  scheduled = 'schedule',
}

export interface RevisionPayload {
  content: string;
  description?: string;
  propertiesUsingAttributes?: Array<{ name: string; attributeValue: string }>;
  deploymentType: RevisionPublishDeploymentType;
}

export interface RevisionResultItem {
  $id: string;
  content: string;
  hash: string;
  created: {
    date: string;
    user: {
      $id: string;
      fullName: string;
      email: string;
    };
  };
  description: string;
  currentRevisionDisplayName: string;
}
export interface UpdateSchedulePayload {
  scheduleIds: string[];
  status: string;
}

export interface ApprovalRejectPayload {
  id: string;
  status: string;
  comment: string;
  additionalData: {
    approver?: {
      deploymentType: string;
    };
  };
}

export interface EntitySettingsPayload {
  id: string;
  isEnabled: boolean;
  rolesNotRequireApproval: string[];
}

export interface RevisionsResponse {
  results: RevisionResultItem[];
  totalCount: number;
}

export interface InstancesResponse {
  results: Instance[];
  totalCount: number;
}

export interface EntityPendingResponse {
  results: EntityPending[];
  resultsMetadata: any;
}

export interface InstanceStats {
  lastUpdated?: string;
  tenants?: string;
  sites?: string;
  siteTags?: string;
  devices?: string;
}

export interface InstanceSummaryResponse {
  $id: string;
  configFile: string;
  appName: string;
  instanceName: string;
  stats: InstanceStats;
  description: string;
}

export interface AttributeItem {
  name: string;
  attributeValue: string;
  path?: string;
}

export type CustomAttributes = AttributeItem[];

export interface InstanceDetailsResponse {
  $id: string;
  appDescriptorId?: number;
  configFile: string;
  configFileId?: number;
  appName: string;
  instanceName: string;
  stats?: InstanceStats;
  description: string;
  draftContent?: string;
  created: {
    date: string;
    user: {
      $id: string;
      fullName?: string;
      email?: string;
    };
  };
  edited: {
    date: string;
    user: {
      $id: string;
      fullName?: string;
      email?: string;
    };
  };
  draftEdited?: {
    date: string;
    user: {
      $id: string;
      fullName: string;
      email: string;
    };
  };
  currentRevision?: string;
  schemaContent?: string;
  isDeployable: boolean;
  propertiesUsingAttributes?: CustomAttributes;
  schemaType: string;
  configSchemaRenderType: string;
  configurationVersion?: number;
}

export interface EntityPending {
  additionalData: {
    requestor: {
      deploymentType: string;
    };
  };
  comment: string;
  condition: {
    configFileId: number;
    configInstanceId: number;
  };
  createdAt: string;
  createdBy: string;
  entityApprovalSettingId: string;
  entityType: string;
  status: string;
  id: string;
  previousValue: string;
  proposedValue: string;
  tenantId: string;
  updatedAt: string;
  updatedBy: string;
}

export interface Instance {
  $id: string;
  configFile: string;
  appName: string;
  instanceName: string;
  createdOn: string;
  stats: {
    lastUpdated: string;
    tenants?: number;
    sites?: number;
    siteTags?: number;
    devices?: number;
  };
  description?: string;
  siteTags?: string[];
  schemaType: string;
  configSchemaRenderType: string;
  totalRevision: number;
  configFileId?: number;
  appDescriptorId?: number;
}

export interface ApplicationConfig {
  configFileId: string | number;
  configFileName: string;
  configFileDottedString: string;
  configFileType: string;
  configSchemaRenderType: string;
  schemaType: string;
}

export interface DeviceConfig {
  appDescriptorId: string | number;
  applicationName: string;
  applicationConfigs: ApplicationConfig[];
  isOpen?: boolean;
}

export interface CreateInstancePayload {
  configFileId: string;
  appDescriptorId: string;
  instanceName: string;
  description?: string;
  draftContent?: string;
}

export interface UpdateInstancePayload {
  instanceName?: string;
  description?: string;
  draftContent?: string;
  propertiesUsingAttributes?: Array<{ name: string; attributeValue: string }>;
  deploymentType?: string;
}

export interface CreateInstanceResponse {
  $id: string;
  created: {
    date: string;
    user: {
      $id: string;
    };
  };
  edited: {
    date: string;
    user: {
      $id: string;
    };
  };
  appName: string;
  instanceName: string;
  stats: {
    lastUpdated: string;
  };
}

export type InstanceItemProps = {
  instance: Partial<Instance>;
  fetchCsv?: (instanceId: string, name: string) => void;
  handleShowAssignment?: (
    value1: string,
    value2: InstanceDetailsResponse
  ) => void;
  approvalFlowStatus?: EntityPending[];
  refetchApprovalFlowStatus?: () => Promise<any>;
};

export type InstanceItemStats = {
  applicable: number;
  compatible: number;
  deploymentNeeded: number;
  matching: number;
  overriden: number;
  pendingDeployment: number;
};

export type ConfigInstanceItemStats = {
  desiredConfigFileInstanceId: string;
  tenantId: string;
  deviceCount: string;
  matchingDeviceCount: string;
  notMatchingDeviceCount: string;
  deployingDeviceCount: string;
};

export type ConfigInstanceItemsStats = {
  resultsMetadata: ResultMetadata;
  results: ConfigInstanceItemStats[];
};

export interface AssignmentItem {
  assignmentId?: string;
  assignmentName?: string;
  assignmentValue: string;
  name?: string;
  id?: string;
  siteId?: string;
  tags?: {
    id: string;
    name: string;
  }[];
}

export enum HierarchyLevelEnum {
  Tenant = 'Tenant',
  SiteTag = 'SiteTag',
  Site = 'Site',
  Device = 'Device',
}

export interface Assignments {
  hierarchyLevel: HierarchyLevelEnum;
  assignments: AssignmentItem[];
}

export interface SiteAsset {
  compatibleAsset: number;
  address: string;
  id: string;
  name: string;
  tags: {
    id: string;
    name: string;
  }[];
}
export interface SiteAssetResponse {
  resultsMetadata: {
    totalResults: number;
    pageIndex: number;
    pageSize: number;
  };
  results: SiteAsset[];
}

export interface SiteTagAsset {
  formattedAddress: string;
  sites: number;
  id: string;
  name: string;
}

export interface SiteTagAssetResponse {
  resultsMetadata: {
    totalResults: number;
    pageIndex: number;
    pageSize: number;
  };
  results: SiteTagAsset[];
}

export interface DeviceAssetData {
  serial: string;
  type: string;
  version: string;
  site: string;
  siteId: string;
  siteName: string;
  deviceId: string;
  deviceName: string;
}

export interface DeviceAssetResponse {
  resultsMetadata: {
    totalResults: number;
    pageIndex: number;
    pageSize: number;
  };
  results: DeviceAssetData[];
}

export interface Row {
  id?: string;
  name?: string;
  assignmentValue?: string;
  serial?: string;
  type?: string;
  version?: string;
  site?: string;
  sites?: number;
  compatibleAsset?: number;
  address?: string;
  checked?: boolean;
}

export interface DeploymentDetail {
  $id: string;
  revisionId: string;
  revisionDisplayName: string;
  deploymentStatus: 'Active' | 'Completed';
  assetsCount: number;
  created: {
    date: string;
    user: {
      $id: string;
      fullName?: string;
      email?: string;
    };
  };
  deviceJobs: {
    deviceId: string;
    assetName: string;
    assetSerialNumber: string;
    siteId: string;
    siteName: string;
    assignmentId: string;
    jobId: string;
    jobStatus: 'Pending' | 'Failed' | 'Successful' | 'Cancelled';
  }[];
  instanceName: string;
  configFileName: string;
  stats: {
    completionPercent: number;
    totalSites: number;
    pendingCount: number;
    failedCount: number;
    successfulCount: number;
    cancelledCount: number;
  };
}

export interface IcsUser {
  $id: string;
  fullName?: string | undefined;
  email?: string | undefined;
}

export interface ChangeDetail {
  date: string;
  user: IcsUser;
}

export enum DeploymentStatus {
  Active = 'Active',
  Completed = 'Completed',
}

export interface DeploymentStats {
  stats: {
    Cancelled: number;
    Failed: number;
    Pending: number;
    Successful: number;
  };
}

export interface ConfigDeploymentResponse {
  $id: string;
  revisionId: string;
  revisionDisplayName: string;
  deploymentStatus: DeploymentStatus;
  assetsCount: number;
  siteCount: number;
  created: ChangeDetail;
}

export type CardRowProps = Partial<Instance>;

export type TabType = keyof typeof Tabs;

export interface ConfigInstanceStats {
  compatible: number;
  overridden: number;
  applicable: number;
  matching: number;
  deploymentNeeded: number;
  pendingDeployment: number;
}

// seems repeated but the props may vary later, will fix later
export interface ConfigAssignmentStats {
  applicable: number;
  overridden: number;
  compatible?: number;
  matching?: number;
  deploymentNeeded?: number;
  pendingDeployment?: number;
}

export interface EntityHierarchyInputs {
  instanceId: string;
  hierarchyType: string;
  hierarchyValue: string;
}

export type ApprovalStatus = {
  status: string;
};
export interface JSONSchema {
  $schema: string;
  $id: string;
  title: string;
  description: string;
  type: string;
  properties: {
    [key: string]: { type: string };
  };
  required: string[];
}

export interface ResultMetadata {
  totalResults: number;
  pageIndex: number;
  pageSize: number;
}

export type SchemaRendererType = 'raw' | 'form' | 'table';

export type InstancesRequest = {
  appDescriptorId?: string;
  configFileId?: string;
  createdBy?: string;
  editedBy?: string;
  approvalStatus?: string;
  instanceName?: string;
  order?: InstanceSortOrder;
  orderBy?: InstanceSortOrderBy;
  pageNumber?: number;
};

type GetUsersUserCompany = {
  id: string;
  name: string;
  featureFlags: FeatureFlags[];
};

export type GetUsersUser = {
  id: string;
  email: string;
  emailVerified: boolean;
  mfaConfigured: boolean;
  status: number;
  fullName: string;
  company: GetUsersUserCompany;
  roles: UserRoles[];
};

type GetUsersResultsMetadata = {
  totalResults: number;
  pageIndex: number;
  pageSize: number;
};

export type GetUsersResponse = {
  resultsMetadata: GetUsersResultsMetadata;
  results: GetUsersUser[];
};

export type GetUsersRequest = {
  fullName?: string;
  pageIndex?: number;
  pageNumber?: number;
  pageSize?: number;
  pending?: boolean;
  roles?: string[];
};

type CompanyInToken = {
  featureFlags: FeatureFlags[];
  id: string;
  name: string;
};

export type Token = IcsUser & {
  aud: string;
  company: CompanyInToken;
  created: number;
  exp: Date;
  iat: Date;
  iss: string;
  jti: string;
  nbf: Date;
  roles: UserRoles[];
  sub: string;
  type: string;
};

export interface GetMaintenanceWindow {
  from: string;
  to: string;
  isNext: boolean;
}

export type AssignmentInLevel = {
  [key in HierarchyLevelEnum]: AssignmentItem[];
};

export type TagNameFilter = {
  tagName: {
    $containsi: string;
  };
};

export type SiteNameFilter = {
  siteName: {
    $containsi: string;
  };
};

export type DeviceNameFilter = {
  name: {
    $containsi: string;
  };
};

export type AuditEventDetailsRequest = {
  eventType?: string;
  deploymentType?: string;
  createdBy?: string;
  createdAt?: string;
  id?: string;
};

interface Condition {
  configFileId: number;
  configInstanceId: number;
}

interface AdditionalData {
  requester: {
    deploymentType: string;
  };
}

export interface EntityPendingPayload {
  condition: Condition;
  entityApprovalSettingId: number;
  status: string;
  proposedValue: string;
  previousValue: string;
  additionalData: AdditionalData;
}

export interface UserGroup {
  usercount?: number;
  id: string;
  name: string;
}

export type IcsUserDetails = {
  id: string;
  email: string;
  emailVerified: boolean;
  mfaConfigured: boolean;
  status: number;
  fullName: string;
  lastLocked: null;
  failedLoginAttempts: number;
  persona: string;
  company: {
    id: string;
    name: string;
    featureFlags: string[];
  };
  roles: string[];
  userGroups: Omit<UserGroup, 'usercount'>[];
  color?: string;
  letters?: string;
  isAdmin?: boolean;
  userGroupCount?: number;
  sub?: string;
};